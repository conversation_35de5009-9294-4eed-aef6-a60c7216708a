import 'dart:convert';
import 'dart:math';
import 'package:com.mynotify.app/core/widgets/global_alert.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../../core/config/api_config.dart';
import '../../models/sms_message.dart';
import '../native_logger.dart';

class ApiService {
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  late final Dio _dio;
  String? _authToken;
  String? _deviceId;
  String? _appVersion;
  bool _initialized = false;
  final _logger = NativeLogger();
  // getter intialized
  bool get initialized => _initialized;

  Future<void> initialize({String? authToken, String? deviceId}) async {
    if (!_initialized) {
      _authToken = authToken;
      _deviceId = deviceId;

      final packageInfo = await PackageInfo.fromPlatform();
      _appVersion = packageInfo.version;

      _dio = Dio(
        BaseOptions(
          baseUrl: ApiConfig.apiDomain,
          connectTimeout: const Duration(seconds: 30),
          receiveTimeout: const Duration(seconds: 30),
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'X-Device-Id': _deviceId,
            'X-App-Version': _appVersion,
            if (_authToken != null) 'Authorization': 'Bearer $_authToken',
          },
        ),
      );

      if (kDebugMode) {
        _dio.interceptors.add(
          LogInterceptor(
            requestBody: true,
            responseBody: true,
            requestHeader: true,
            responseHeader: true,
            logPrint: (obj) => debugPrint(obj.toString()),
          ),
        );
      }

      _initialized = true;
    }
  }

  Future<bool> sendSmsToBackend(
    SmsMessage smsMessage, {
    String? receiverNumber,
  }) async {
    await _logger.logSms(
      'SMS send to backend started',
      details: {
        'id': smsMessage.id,
        'sender': smsMessage.sender,
        'simSlot': smsMessage.simSlot,
        'subscriptionId': smsMessage.subscriptionId,
        'receiverNumber': receiverNumber ?? 'null',
      },
    );

    // First check for basic connectivity
    final connectivityResult = await Connectivity().checkConnectivity();
    final hasNetworkConnection = connectivityResult.any(
      (result) => result != ConnectivityResult.none,
    );

    if (!hasNetworkConnection) {
      debugPrint('No network connection available, skipping SMS send');
      await _logger.logNetwork(
        'No network connection for SMS send',
        details: {
          'smsId': smsMessage.id,
          'connectivityResult': connectivityResult.toString(),
        },
      );
      return false;
    }

    const maxRetries = 15;
    int retryCount = 0;

    // Prepare request data outside try block for error logging
    final requestData = {
      'id': smsMessage.id,
      'sender': smsMessage.sender,
      'body': smsMessage.body,
      'timestamp': smsMessage.timestamp.millisecondsSinceEpoch,
      'simSlot': smsMessage.simSlot,
      'subscriptionId': smsMessage.subscriptionId,
      'deviceId': _deviceId,
      if (receiverNumber != null) 'receiver_number': receiverNumber,
    };

    while (retryCount <= maxRetries) {
      try {
        if (retryCount > 0) {
          await _logger.logSms(
            'SMS send retry attempt',
            details: {
              'attempt': retryCount + 1,
              'maxRetries': maxRetries + 1,
              'smsId': smsMessage.id,
            },
          );
        }

        await _logger.logApiRequest(
          url: '${ApiConfig.apiDomain}/api/transaction/verify',
          method: 'POST',
          payload: jsonEncode(requestData),
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'X-Device-Id': _deviceId ?? 'unknown',
          },
        );

        final response = await _dio.post(
          '/api/transaction/verify',
          data: requestData,
        );

        await _logger.logApiRequest(
          url: '${ApiConfig.apiDomain}/api/transaction/verify',
          method: 'POST',
          statusCode: response.statusCode,
          response: response.data.toString(),
          headers: {
            'Response-Time':
                response.headers.value('response-time') ?? 'unknown',
            'Content-Type': response.headers.value('content-type') ?? 'unknown',
          },
        );

        final success = response.data['success'] == true;
        await _logger.logSms(
          success ? 'SMS sent successfully' : 'SMS send failed',
          details: {
            'smsId': smsMessage.id,
            'success': success,
            'attempt': retryCount + 1,
          },
        );

        return success;
      } on DioException catch (e) {
        debugPrint(
          'Error sending SMS to backend (attempt ${retryCount + 1}/$maxRetries): ${e.message}',
        );

        await _logger.logApiRequest(
          url: '${ApiConfig.apiDomain}/api/transaction/verify',
          method: 'POST',
          statusCode: e.response?.statusCode,
          error: e.message ?? 'Network error',
          response: e.response?.data?.toString(),
          headers: {
            'Error-Type': e.type.toString(),
            'Request-Payload': jsonEncode(requestData),
          },
        );

        if (e.response != null) {
          debugPrint('Response data: ${e.response?.data}');
          debugPrint('Response status: ${e.response?.statusCode}');

          // Don't retry on client errors (4xx)
          if (e.response!.statusCode != null &&
              e.response!.statusCode! >= 400 &&
              e.response!.statusCode! < 500) {
            await _logger.logSms(
              'SMS send failed - client error',
              details: {
                'smsId': smsMessage.id,
                'statusCode': e.response!.statusCode,
                'error': e.message,
                'attempt': retryCount + 1,
              },
            );
            return false;
          }
        }

        // If we've reached max retries, return false
        if (retryCount == maxRetries) {
          await _logger.logSms(
            'SMS send failed - max retries reached',
            details: {
              'smsId': smsMessage.id,
              'maxRetries': maxRetries + 1,
              'error': e.message,
            },
          );
          return false;
        }

        // Exponential backoff with jitter
        final delay = min(300000, (pow(2, retryCount) * 1000).toInt());
        final jitter = Random().nextInt(1000);
        await Future.delayed(Duration(milliseconds: delay + jitter));

        retryCount++;
      } catch (e) {
        debugPrint(
          'Unexpected error sending SMS (attempt ${retryCount + 1}/$maxRetries): $e',
        );

        // If we've reached max retries, return false
        if (retryCount == maxRetries) {
          return false;
        }

        // Exponential backoff with jitter
        final delay = min(300000, (pow(2, retryCount) * 1000).toInt());
        final jitter = Random().nextInt(1000);
        await Future.delayed(Duration(milliseconds: delay + jitter));

        retryCount++;
      }
    }

    return false;
  }

  Future<Map<String, dynamic>> login(String email, String password) async {
    await _logger.logAuth('Login attempt started', details: {'email': email});

    try {
      final requestData = {
        'email': email,
        'password': password,
        // 'deviceId': _deviceId,
        // 'deviceType': 'android',
      };

      await _logger.logApiRequest(
        url: '${ApiConfig.apiDomain}/api/auth/login',
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        payload: jsonEncode(requestData),
      );

      final response = await _dio.post(
        '/api/auth/login',
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            // 'Accept': 'application/json',
          },
        ),
        data: requestData,
      );

      await _logger.logApiRequest(
        url: '${ApiConfig.apiDomain}/api/auth/login',
        method: 'POST',
        statusCode: response.statusCode,
        response: response.data.toString(),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['data'] != null) {
          _authToken = data['data']['token'];
          _dio.options.headers['Authorization'] = 'Bearer $_authToken';
          await _logger.logAuth('Login successful', details: {'email': email});
          return {'success': true, 'data': data['data']};
        }
      }

      await _logger.logAuth(
        'Login failed',
        details: {
          'email': email,
          'message': response.data['message'] ?? 'Login failed',
        },
      );

      return {
        'success': false,
        'message': response.data['message'] ?? 'Login failed',
      };
    } on DioException catch (e) {
      debugPrint('Login error: ${e.message}');

      await _logger.logApiRequest(
        url: '${ApiConfig.apiDomain}/api/auth/login',
        method: 'POST',
        statusCode: e.response?.statusCode,
        error: e.message ?? 'Network error',
        response: e.response?.data?.toString(),
      );

      await _logger.logAuth(
        'Login network error',
        details: {
          'email': email,
          'error': e.message ?? 'Network error occurred',
        },
      );

      return {
        'success': false,
        'message': e.response?.data?['message'] ?? 'Network error occurred',
      };
    } catch (e, stackTrace) {
      debugPrint('Unexpected login error: $e : $stackTrace');

      await _logger.logError(
        'Unexpected login error: $e',
        stackTrace: stackTrace,
        details: {'email': email},
      );

      return {'success': false, 'message': 'An unexpected error occurred'};
    }
  }

  Future<void> uploadLogs({
    required ValueNotifier<bool> isLoadingNotifier,
    required String userName,
    required String userEmail,
  }) async {
    final apiService = ApiService();
    final logger = NativeLogger();

    try {
      isLoadingNotifier.value = true;
      // Ensure ApiService initialized before using it
      if (!apiService.initialized) {
        await apiService.initialize();
      }

      // Step 1: Locate log file
      final logFile = await logger.getShareableLogFile();

      await logger.log(
        'LOG FILE',
        'Preparing log file upload: IsFILE NULL : ${logFile == null} & path: ${logFile?.path}',
      );
      if (logFile == null) {
        await logger.log('LOG FILE', 'Log file not found');
        return;
      }

      // Step 2: Build multipart form data
      final formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(
          logFile.path,
          filename:
              'Log_file_${DateTime.now().year}-${DateTime.now().month}-${DateTime.now().day}-${DateTime.now().hour}-${DateTime.now().minute}-${DateTime.now().second}.txt',
        ),
      });

      // add name and email in formData
      formData.fields.add(MapEntry('userName', userName));
      formData.fields.add(MapEntry('email', userEmail));

      // Step 3: Send request using the shared Dio instance
      final response = await apiService._dio.post(
        '/api/user/log-upload',
        data: formData,
        options: Options(
          headers: {
            'Content-Type': 'multipart/form-data',
            'Accept': 'application/json',
            // if (apiService.initialized && apiService._authToken != null)
            //   'Authorization': 'Bearer ${apiService._authToken}',
          },
        ),
      );

      // Step 4: Handle success
      if (response.statusCode == 200) {
        await logger.log(
          'LOG FILE',
          'Log file uploaded successfully, response: ${response.data}, statusCode: ${response.statusCode}',
        );

        // Clear ALL logs after successful upload (including current day's logs)
        await _logger.clearAllLogs();

        isLoadingNotifier.value = false;

        GlobalAlert.show(
          title: 'Success',
          message: 'Log file uploaded successfully',
          type: AlertType.success,
        );
      } else {
        isLoadingNotifier.value = false;
        await logger.log(
          'LOG FILE',
          'Log upload failed, response: ${response.data}, statusCode: ${response.statusCode}',
        );

        GlobalAlert.show(
          title: 'Error',
          message: 'Failed to upload log file (${response.statusCode})',
          type: AlertType.error,
        );
      }
    } on DioException catch (e) {
      isLoadingNotifier.value = false;
      debugPrint('Error uploading log file: ${e.message}');

      await logger.logApiRequest(
        url: '${ApiConfig.apiDomain}${ApiConfig.sendTextFileEndpoint}',
        method: 'POST',
        statusCode: e.response?.statusCode,
        error: e.message ?? 'Network error',
        response: e.response?.data?.toString(),
        headers: {
          'Error-Type': e.type.toString(),
          'Request-Type': 'multipart/form-data',
        },
      );

      GlobalAlert.show(
        title: 'Error',
        message: 'Failed to upload log file: ${e.message}',
        type: AlertType.error,
      );
    } catch (e) {
      isLoadingNotifier.value = false;
      await logger.log(
        'LOG FILE',
        'Unexpected error during log upload -- error: ${e.toString()}',
      );

      GlobalAlert.show(
        title: 'Error',
        message: 'Failed to upload log file: ${e.toString()}',
        type: AlertType.error,
      );
    }
  }

  // Verify OTP for phone number
  Future<Map<String, dynamic>> verifyOtp(
    String phoneNumber,
    String otp, {
    int? simSlot,
  }) async {
    try {
      final response = await _dio.post(
        '/api/auth/verify-otp',
        data: {
          'phoneNumber': phoneNumber,
          'otp': otp,
          'deviceId': _deviceId,
          'simSlot': simSlot,
        },
      );

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data['data'],
          'message': response.data['message'],
        };
      }
      return {'success': false, 'message': response.data['message']};
    } on DioException catch (e) {
      debugPrint('Verify OTP error: ${e.message}');
      return {
        'success': false,
        'message': e.response?.data?['message'] ?? 'Failed to verify OTP',
      };
    } catch (e) {
      debugPrint('Unexpected verify OTP error: $e');
      return {'success': false, 'message': 'An unexpected error occurred'};
    }
  }

  // Verify phone number with OTP
  Future<Map<String, dynamic>> verifyPhoneNumber(
    String phoneNumber,
    String otp,
    int simSlot,
  ) async {
    try {
      final response = await _dio.post(
        '/api/auth/verify-phone',
        data: {
          'phoneNumber': phoneNumber,
          'otp': otp,
          'simSlot': simSlot,
          'deviceId': _deviceId,
        },
      );

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data['data'],
          'message': response.data['message'],
        };
      }
      return {'success': false, 'message': response.data['message']};
    } on DioException catch (e) {
      debugPrint('Verify phone error: ${e.message}');
      return {
        'success': false,
        'message':
            e.response?.data?['message'] ?? 'Failed to verify phone number',
      };
    } catch (e) {
      debugPrint('Unexpected verify phone error: $e');
      return {'success': false, 'message': 'An unexpected error occurred'};
    }
  }

  // Check for app updates
  Future<Map<String, dynamic>> checkForUpdates() async {
    try {
      final response = await _dio.get(
        '/api/version/DEPOSIT',
        options: Options(headers: {'application-key': 'tagline'}),
        // queryParameters: {'currentVersion': _appVersion, 'platform': 'android'},
      );
      if (response.statusCode == 200) {
        return {
          'success': true,

          /// This latest version is fetch by current_version key
          'latest_version': response.data['data']['current_version'] ?? '',
        };
      }
      return {'success': false, 'message': 'Failed to check for updates'};
    } on DioException catch (e) {
      debugPrint('Check update error: ${e.message}');
      return {'success': false, 'message': 'Failed to check for updates'};
    } catch (e) {
      debugPrint('Unexpected check update error: $e');
      return {'success': false, 'message': 'An unexpected error occurred'};
    }
  }

  /// Download APK file
  Future<Response<dynamic>> downloadApkFile(
    String url,
    dynamic savePath, {
    void Function(int, int)? onReceiveProgress,
  }) async {
    try {
      return _dio.download(
        url,
        savePath,
        onReceiveProgress: onReceiveProgress,
        options: Options(headers: {'application-key': 'tagline'}),
      );
    } catch (e) {
      debugPrint('Error downloading APK file: $e');
      throw Exception('Failed to download APK file.');
    }
  }

  // Logout user
  // Future<bool> logout() async {
  //   try {
  //     await _dio.post('/api/auth/logout');
  //     return true;
  //   } catch (e) {
  //     debugPrint('Logout error: $e');
  //     return false;
  //   }
  // }

  Future<Map<String, dynamic>> sendOtp(
    String phoneNumber,
    String countryCode, {
    int? simSlot,
  }) async {
    try {
      // Generate OTP
      final otp = _generateOTP();

      // Get user IP
      String userIp = '0.0.0.0';
      try {
        final ipResponse = await _dio.get(ApiConfig.ipLocationDomain);
        if (ipResponse.statusCode == 200) {
          userIp = ipResponse.data['ip'] ?? '0.0.0.0';
        }
      } catch (e) {
        debugPrint('Error getting IP address: $e');
      }

      final response = await _dio.post(
        ApiConfig.otpConfig['apiUrl']!,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'clientId': ApiConfig.otpConfig['clientId'],
            'clientSecret': ApiConfig.otpConfig['clientSecret'],
          },
        ),
        data: {
          'to': phoneNumber,
          'otp': otp,
          'country_code': countryCode,
          'domain': ApiConfig.otpConfig['domain'],
          'userIp': userIp,
          if (simSlot != null) 'simSlot': simSlot,
        },
      );

      if (response.statusCode == 200) {
        return {
          'success': true,
          'message': 'OTP sent successfully',
          'data': response.data,
        };
      } else {
        return {
          'success': false,
          'message': response.data['message'] ?? 'Failed to send OTP',
        };
      }
    } catch (e) {
      debugPrint('Error sending OTP: $e');
      return {'success': false, 'message': 'Failed to send OTP: $e'};
    }
  }

  String _generateOTP() {
    final random = DateTime.now().millisecondsSinceEpoch % 900000 + 100000;
    return random.toString();
  }

  Future<List<SmsMessage>> getPendingMessages() async {
    try {
      final response = await _dio.get('/api/sms/pending');

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['messages'] ?? [];
        return data.map((json) => SmsMessage.fromJson(json)).toList();
      }
      return [];
    } catch (e) {
      debugPrint('Error getting pending messages: $e');
      return [];
    }
  }

  Future<bool> updateUserProfile(Map<String, dynamic> profileData) async {
    try {
      final response = await _dio.put('/api/user/profile', data: profileData);

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('Error updating user profile: $e');
      return false;
    }
  }
}
