import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:com.mynotify.app/providers/auto_update_provider.dart';

/// Auto update screen
class AutoUpdateScreen extends StatelessWidget {
  const AutoUpdateScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Container(
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage('assets/images/update_bg.png'),
            fit: BoxFit.cover,
          ),
        ),
        child: Scaffold(
          backgroundColor: Colors.transparent,
          body: SafeArea(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Consumer<AutoUpdateProvider>(
                builder: (context, provider, _) {
                  if (!provider.updateAvailable &&
                      !provider.updateDownloaded &&
                      !provider.isDownloading) {
                    // Equivalent of "waiting until checkForUpdate() completes"
                    return const SizedBox.shrink();
                  }

                  return Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      SizedBox(height: 40),
                      Text(
                        'New Update Available',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: 8),
                      Text(
                        provider.latestVersion ?? '',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const Spacer(),
                      Padding(
                        padding: const EdgeInsets.all(12),
                        child: Column(
                          children: [
                            Text(
                              'Update Description',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            SizedBox(height: 8),
                            Text(
                              'Current app version: ${provider.currentVersion}, please update to latest version.',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.black,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                      // Show progress if downloading
                      if (provider.isDownloading) ...[
                        LinearProgressIndicator(
                          value: provider.progress,
                          backgroundColor: Colors.black54,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.blue,
                          ),

                          borderRadius: BorderRadius.circular(10),
                        ),
                        Text(
                          'Downloading...',
                          style: TextStyle(fontSize: 16, color: Colors.black),
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: 16),
                      ],

                      // Show download button if no download in progress yet
                      if (!provider.isDownloading && !provider.updateDownloaded)
                        ElevatedButton(
                          onPressed: () => provider.downloadUpdate(),
                          child: const Text(
                            'Download Update',
                            style: TextStyle(color: Colors.black),
                          ),
                        ),

                      // Show install button once downloaded
                      if (provider.updateDownloaded)
                        ElevatedButton(
                          onPressed: () async {
                            provider.installUpdate();
                          },
                          child: const Text('Install'),
                        ),
                    ],
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );
  }
}
