import 'dart:async';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:pinput/pinput.dart';
import 'package:provider/provider.dart';
import 'package:country_code_picker/country_code_picker.dart';
import '../../../../providers/auth_provider.dart';
import '../../../../models/user.dart';
import '../../../../core/config/api_config.dart';
import '../../../../core/widgets/global_alert.dart';

class PhoneAuthScreen extends StatefulWidget {
  final dynamic
  selectedSlot; // Changed to dynamic to handle the new data structure

  const PhoneAuthScreen({super.key, required this.selectedSlot});

  @override
  State<PhoneAuthScreen> createState() => _PhoneAuthScreenState();
}

class _PhoneAuthScreenState extends State<PhoneAuthScreen> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final TextEditingController _otpController = TextEditingController();
  final FocusNode _otpFocusNode = FocusNode();
  final List<TextEditingController> _otpControllers = List.generate(
    6,
    (index) => TextEditingController(),
  );
  final List<FocusNode> _otpFocusNodes = List.generate(
    6,
    (index) => FocusNode(),
  );
  bool _isLoading = false;
  bool _otpSent = false;
  String? _error;
  String _selectedCountryCode =
      '+880'; // Default to Bangladesh like React Native
  String? _generatedOtp; // Store the generated OTP locally
  int _countdown = 60;
  bool _isResendActive = false;

  @override
  void dispose() {
    _phoneController.dispose();
    for (var controller in _otpControllers) {
      controller.dispose();
    }
    for (var focusNode in _otpFocusNodes) {
      focusNode.dispose();
    }
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    // Start countdown timer when OTP is sent
    if (_otpSent) {
      _startCountdown();
    }
  }

  void _startCountdown() {
    _countdown = 60;
    _isResendActive = false;

    Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_countdown > 0 && mounted) {
        setState(() {
          _countdown--;
        });
      } else {
        if (mounted) {
          setState(() {
            _isResendActive = true;
          });
        }
        _isResendActive = true;
        timer.cancel();
      }
    });
  }

  String _generateOTP() {
    final random = DateTime.now().millisecondsSinceEpoch % 900000 + 100000;
    return random.toString();
  }

  Future<void> _sendOtp() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Generate OTP locally like React Native
      final newOtp = _generateOTP();
      debugPrint('Generated OTP: $newOtp');

      // Get user IP
      String userIp = '0.0.0.0';
      final dio = Dio();
      try {
        final ipResponse = await dio.get(ApiConfig.ipLocationDomain);
        if (ipResponse.statusCode == 200) {
          userIp = ipResponse.data['ip'] ?? '0.0.0.0';
        }
      } catch (e) {
        debugPrint('Error getting IP address: $e');
      }

      // Send OTP via external service (like React Native)
      final response = await dio.post(
        ApiConfig.otpConfig['apiUrl']!,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'clientId': ApiConfig.otpConfig['clientId'],
            'clientSecret': ApiConfig.otpConfig['clientSecret'],
          },
        ),
        data: {
          'to': _phoneController.text.trim(),
          'otp': newOtp,
          'country_code': _selectedCountryCode,
          'domain': ApiConfig.otpConfig['domain'],
          'userIp': userIp,
        },
      );

      if (response.statusCode == 200) {
        setState(() {
          _generatedOtp = newOtp;
          _otpSent = true;
        });

        _startCountdown();

        if (mounted) {
          GlobalAlert.show(
            title: 'Success',
            message: 'OTP sent successfully!',
            type: AlertType.success,
          );
        }
      } else {
        throw Exception(response.data['message'] ?? 'Failed to send OTP');
      }
    } catch (e) {
      setState(() {
        _error = e.toString().replaceAll('Exception: ', '');
      });

      if (mounted) {
        GlobalAlert.show(
          title: 'Error',
          message: _error ?? 'An error occurred',
          type: AlertType.error,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _verifyOtp() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Verify OTP locally like React Native
      final enteredOtp = _otpController.text.trim();
      if (enteredOtp != _generatedOtp) {
        throw Exception('Invalid verification code');
      }

      // Update user state with verified SIM info (like React Native)
      final authProvider = context.read<AuthProvider>();
      final user = authProvider.user;

      if (user != null) {
        // Create new SIM verification entry
        final newSimVerification = VerifiedSim(
          phoneNumber: '$_selectedCountryCode${_phoneController.text.trim()}',
          subscriptionId: widget.selectedSlot['subscriptionId'] ?? 0,
          iccId: widget.selectedSlot['iccId'] ?? '',
          slotIndex: widget.selectedSlot['slotIndex'] ?? 0,
          carrierName: widget.selectedSlot['carrierName'] ?? '',
          verifiedAt: DateTime.now().toIso8601String(),
          simSignature:
              '${widget.selectedSlot['subscriptionId']}-${widget.selectedSlot['iccId']}-${widget.selectedSlot['slotIndex']}',
          isVerified: true,
          simSerialNumber: widget.selectedSlot['simSerialNumber'] ?? '',
        );

        await authProvider.updateSimInfo(newSimVerification);

        if (mounted) {
          GlobalAlert.show(
            title: 'Success',
            message: 'SIM card verified successfully!',
            type: AlertType.success,
          );

          Navigator.of(context).pop(true);
        }
      }
    } catch (e) {
      setState(() {
        _error = e.toString().replaceAll('Exception: ', '');
      });

      if (mounted) {
        GlobalAlert.show(
          title: 'Error',
          message: _error ?? 'An error occurred',
          type: AlertType.error,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _resendOtp() async {
    if (!_isResendActive) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Generate new OTP
      final newOtp = _generateOTP();
      debugPrint('Resend OTP: $newOtp');

      // Get user IP
      String userIp = '0.0.0.0';
      final dio = Dio();
      try {
        final ipResponse = await dio.get(ApiConfig.ipLocationDomain);
        if (ipResponse.statusCode == 200) {
          userIp = ipResponse.data['ip'] ?? '0.0.0.0';
        }
      } catch (e) {
        debugPrint('Error getting IP address: $e');
      }

      // Send OTP via external service
      final response = await dio.post(
        ApiConfig.otpConfig['apiUrl']!,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'clientId': ApiConfig.otpConfig['clientId'],
            'clientSecret': ApiConfig.otpConfig['clientSecret'],
          },
        ),
        data: {
          'to': _phoneController.text.trim(),
          'otp': newOtp,
          'country_code': _selectedCountryCode,
          'domain': ApiConfig.otpConfig['domain'],
          'userIp': userIp,
        },
      );

      if (response.statusCode == 200) {
        setState(() {
          _generatedOtp = newOtp;
        });

        _startCountdown();

        if (mounted) {
          ScaffoldMessenger.of(context)
            ..clearSnackBars()
            ..showSnackBar(
              const SnackBar(
                content: Text('Verification code resent successfully'),
                backgroundColor: Colors.green,
              ),
            );
        }
      } else {
        throw Exception(response.data['message'] ?? 'Failed to resend OTP');
      }
    } catch (e) {
      setState(() {
        _error = e.toString().replaceAll('Exception: ', '');
      });

      if (mounted) {
        GlobalAlert.show(
          title: 'Error',
          message: _error ?? 'An error occurred',
          type: AlertType.error,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Phone Verification')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // SIM Slot Info Card
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    children: [
                      const CircleAvatar(
                        backgroundColor: Colors.blue,
                        child: Icon(Icons.sim_card, color: Colors.white),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.selectedSlot['displayName'] ?? 'SIM Card',
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                              ),
                            ),
                            Text(
                              'Selected for verification',
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),

              if (_error != null) ...[
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.red.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.red.shade200),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.error, color: Colors.red.shade700),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _error!,
                          style: TextStyle(color: Colors.red.shade700),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
              ],

              // Phone number + country code
              Row(
                children: [
                  Flexible(
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(4.0),
                      ),
                      child: AbsorbPointer(
                        absorbing: kDebugMode ? false : true,
                        // absorbing: false,
                        child: CountryCodePicker(
                          onChanged: (country) {
                            setState(() {
                              _selectedCountryCode = country.dialCode ?? '+880';
                            });
                          },
                          initialSelection: 'BD',
                          favorite: const ['+880', 'BD'],
                          showCountryOnly: false,
                          showOnlyCountryWhenClosed: false,
                          alignLeft: false,
                          showFlag: false,
                          showFlagDialog: true,
                          // padding: EdgeInsets.zero,
                          textStyle: const TextStyle(
                            color: Colors.black,
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    flex: 3,
                    child: TextFormField(
                      controller: _phoneController,
                      keyboardType: TextInputType.phone,
                      enabled: !_otpSent,
                      decoration: const InputDecoration(
                        labelText: 'Phone Number',
                        border: OutlineInputBorder(),
                        hintText: '9876543210',
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your phone number';
                        }
                        final digitsOnly = value.replaceAll(RegExp(r'\D'), '');
                        if (digitsOnly.length != 10) {
                          return 'Phone number must be 10 digits';
                        }
                        // // Bangladesh phone number validation (like React Native)
                        // if (!RegExp(r'^1[3-9]\d{8}$').hasMatch(digitsOnly)) {
                        //   return 'Invalid Bangladeshi phone number';
                        // }
                        return null;
                      },
                      onChanged: (text) {
                        // Clean phone number like React Native
                        String cleaned = text.replaceAll(RegExp(r'[^0-9]'), '');
                        // Remove all leading zeros
                        cleaned = cleaned.replaceAll(RegExp(r'^0+'), '');
                        if (cleaned != text) {
                          _phoneController.value = TextEditingValue(
                            text: cleaned,
                            selection: TextSelection.collapsed(
                              offset: cleaned.length,
                            ),
                          );
                        }
                      },
                      onTapOutside: (event) {
                        FocusScope.of(context).unfocus();
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              if (!_otpSent) ...[
                ElevatedButton(
                  onPressed: _isLoading ? null : _sendOtp,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        )
                      : const Text(
                          'Send OTP',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                ),
              ] else ...[
                // OTP Input - 6 separate fields like React Native
                const Text(
                  'Enter Verification Code',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 16),

                // Inside your build method
                Pinput(
                  length: 6,
                  controller:
                      _otpController, // This is a single controller for all digits
                  focusNode: _otpFocusNode,
                  defaultPinTheme: PinTheme(
                    width: 45,
                    height: 55,
                    textStyle: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  focusedPinTheme: PinTheme(
                    width: 45,
                    height: 55,
                    textStyle: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.blue, width: 2),
                      borderRadius: BorderRadius.circular(8),
                      color: Colors.blue.withOpacity(0.1),
                    ),
                  ),
                  validator: (value) {
                    if (value == null || value.length != 6) {
                      return 'Please enter complete OTP';
                    }
                    return null;
                  },
                  onChanged: (value) {
                    // Optional: Live input change
                  },
                  onCompleted: (pin) {
                    // Optional: Called when all 6 digits are entered
                    print('Completed: $pin');
                  },
                ),

                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _isLoading ? null : _verifyOtp,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        )
                      : const Text(
                          'Verify OTP',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      _countdown > 0
                          ? 'Resend code in ${_countdown}s'
                          : "Didn't receive the code?",
                      style: TextStyle(color: Colors.grey[600], fontSize: 14),
                    ),
                    const SizedBox(width: 8),
                    TextButton(
                      onPressed: _isResendActive && !_isLoading
                          ? _resendOtp
                          : null,
                      child: Text(
                        _isLoading ? 'Resending...' : 'Resend',
                        style: TextStyle(
                          color: _isResendActive && !_isLoading
                              ? Colors.blue
                              : Colors.grey,
                        ),
                      ),
                    ),
                  ],
                ),
              ],

              const SizedBox(height: 24),
            ],
          ),
        ),
      ),
    );
  }
}
