import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:com.mynotify.app/features/auto_update/auto_update_screen.dart';
import 'package:com.mynotify.app/providers/auto_update_provider.dart';

import 'providers/auth_provider.dart';
import 'providers/sim_provider.dart';
import 'providers/sms_provider.dart';
import 'providers/permission_provider.dart';
import 'features/auth/presentation/pages/login_screen.dart';
import 'features/home/<USER>/pages/home_screen.dart';
import 'features/sim_auth/presentation/pages/sim_auth_screen.dart';
import 'features/phone_auth/presentation/pages/phone_auth_screen.dart';
import 'core/constants/app_constants.dart';
import 'core/widgets/global_alert.dart';
import 'core/background/pending_sms_service.dart';
import 'models/user.dart';
import 'models/sim_card.dart';
import 'models/sms_message.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Hive for local storage
  await Hive.initFlutter();

  // Register Hive adapters
  Hive.registerAdapter(UserAdapter());
  Hive.registerAdapter(PaymentConfigAdapter());
  Hive.registerAdapter(VerifiedSimAdapter());
  Hive.registerAdapter(SimCardInfoAdapter());
  Hive.registerAdapter(PendingSmsMessageAdapter());
  Hive.registerAdapter(SimInfoAdapter());
  Hive.registerAdapter(SimStateChangeAdapter());
  Hive.registerAdapter(SmsMessageAdapter());
  Hive.registerAdapter(HierarchyAdapter());
  Hive.registerAdapter(SuperAdminAdapter());
  Hive.registerAdapter(ClientAdapter());
  Hive.registerAdapter(PhoneDetailAdapter());
  Hive.registerAdapter(PrimaryArrayNumberAdapter());

  // Request notification permission on startup
  await _requestNotificationPermission();
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitDown,
    DeviceOrientation.portraitUp,
  ]);

  runApp(const MyNotifyApp());
}

Future<void> _requestNotificationPermission() async {
  try {
    final status = await Permission.notification.request();
    debugPrint('Notification permission status: $status');
  } catch (err) {
    debugPrint('Failed to request notification permission: $err');
  }
}

class MyNotifyApp extends StatelessWidget {
  const MyNotifyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => AutoUpdateProvider()),
        ChangeNotifierProvider(create: (_) => SimProvider()),
        ChangeNotifierProvider(
          create: (context) =>
              SmsProvider(authProvider: context.read<AuthProvider>()),
        ),
        ChangeNotifierProvider(create: (_) => PermissionProvider()),
      ],
      child:
          Consumer4<AuthProvider, SimProvider, SmsProvider, AutoUpdateProvider>(
            builder:
                (
                  context,
                  authProvider,
                  simProvider,
                  smsProvider,
                  autoUpdateProvider,
                  _,
                ) {
                  // Initialize the PendingSmsService with the SMS provider
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    PendingSmsService().initialize(smsProvider);
                  });

                  return MaterialApp(
                    title: AppConstants.appName,
                    debugShowCheckedModeBanner: false,
                    navigatorKey: GlobalAlert.navigatorKey,
                    theme: ThemeData(
                      primarySwatch: Colors.blue,
                      useMaterial3: true,
                      appBarTheme: const AppBarTheme(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        elevation: 0,
                      ),
                    ),
                    home: _getInitialScreen(authProvider, autoUpdateProvider),
                    routes: {
                      '/login': (context) => const LoginScreen(),
                      '/home': (context) => const HomeScreen(),
                      '/sim_auth': (context) => const SimAuthScreen(),
                      '/auto_update': (context) => const AutoUpdateScreen(),
                    },
                    onGenerateRoute: (settings) {
                      if (settings.name == '/phone_auth') {
                        final args =
                            settings.arguments as Map<String, dynamic>?;
                        final selectedSlot = args?['selectedSlot'];
                        return MaterialPageRoute(
                          builder: (context) =>
                              PhoneAuthScreen(selectedSlot: selectedSlot),
                        );
                      }
                      return null;
                    },
                  );
                },
          ),
    );
  }

  Widget _getInitialScreen(
    AuthProvider authProvider,
    AutoUpdateProvider autoUpdateProvider,
  ) {
    // If still initializing, show loading indicator
    if (!authProvider.isInitialized || autoUpdateProvider.isChecking) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }
    if (autoUpdateProvider.updateAvailable) {
      return const AutoUpdateScreen();
    }

    return authProvider.isAuthenticated
        ? const HomeScreen()
        : const LoginScreen();
  }
}
