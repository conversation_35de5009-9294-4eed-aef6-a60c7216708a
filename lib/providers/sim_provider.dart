import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:hive/hive.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/sim_card.dart';

class SimProvider extends ChangeNotifier {
  static const MethodChannel _simInfoChannel = MethodChannel(
    'com.mynotify.app/native',
  );
  static const MethodChannel _simStateMonitorChannel = MethodChannel(
    'com.mynotify.app/native',
  );
  static const EventChannel _simStateEventChannel = EventChannel(
    'com.mynotify.app/native/events',
  );

  // Add a method channel for receiving SIM state change events
  static const MethodChannel _simStateChangeChannel = MethodChannel(
    'com.mynotify.app/native/changes',
  );

  SimInfo? _simInfo;
  bool _isMonitoring = false;
  bool _isLoading = false;
  String? _error;
  Box<SimStateChange>? _simStateBox;

  SimInfo? get simInfo => _simInfo;
  bool get isMonitoring => _isMonitoring;
  bool get isLoading => _isLoading;
  String? get error => _error;

  SimProvider() {
    _initializeSimProvider();
    _setupSimStateChangeListener();
  }

  Future<void> _initializeSimProvider() async {
    try {
      _simStateBox = await Hive.openBox<SimStateChange>('sim_box');
      await getSimInfo();
      _setupSimStateListener();
    } catch (e) {
      debugPrint('Error initializing SIM provider: $e');
      _error = e.toString();
      notifyListeners();
    }
  }

  Future<void> getSimInfo() async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      // Check if we have phone permission first
      final phonePermissionStatus = await Permission.phone.status;
      if (!phonePermissionStatus.isGranted) {
        debugPrint('No phone permission to get SIM info');
        _error = 'Phone permission required for accessing SIM information';
        return;
      }

      final result = await _simInfoChannel.invokeMethod('getSimInfo');
      if (result != null) {
        // Handle different possible result types
        Map<String, dynamic> resultMap;
        if (result is Map<String, dynamic>) {
          resultMap = result;
        } else if (result is Map) {
          // Convert Map<Object, Object> to Map<String, dynamic>
          resultMap = result.map<String, dynamic>(
            (key, value) => MapEntry(key.toString(), value),
          );
        } else {
          throw Exception('Unexpected result type: ${result.runtimeType}');
        }
        _simInfo = SimInfo.fromJson(resultMap);
        debugPrint(
          'Successfully retrieved SIM info for ${_simInfo?.activeSimCount ?? 0} SIMs',
        );
      } else {
        _simInfo = null;
        debugPrint('No SIM info returned from native code');
      }
    } catch (e) {
      debugPrint('Error getting SIM info: $e');
      _error = e.toString();
      _simInfo = null; // Clear previous SIM info on error
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> checkSimInfoPermission() async {
    try {
      // Use phone permission for SIM info access
      final status = await Permission.phone.status;
      return status.isGranted;
    } catch (e) {
      debugPrint('Error checking phone permission for SIM info: $e');
      return false;
    }
  }

  Future<bool> startMonitoring() async {
    try {
      final result = await _simStateMonitorChannel.invokeMethod(
        'startMonitoring',
      );
      _isMonitoring = result as bool? ?? false;
      notifyListeners();
      return _isMonitoring;
    } catch (e) {
      debugPrint('Failed to start SIM state monitoring: $e');
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  Future<bool> stopMonitoring() async {
    try {
      final result = await _simStateMonitorChannel.invokeMethod(
        'stopMonitoring',
      );
      _isMonitoring = !(result as bool? ?? true);
      notifyListeners();
      return result as bool? ?? false;
    } catch (e) {
      debugPrint('Failed to stop SIM state monitoring: $e');
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  Future<bool> checkMonitoringStatus() async {
    try {
      final result = await _simStateMonitorChannel.invokeMethod('isMonitoring');
      _isMonitoring = result as bool? ?? false;
      notifyListeners();
      return _isMonitoring;
    } catch (e) {
      debugPrint('Failed to check monitoring status: $e');
      return false;
    }
  }

  void _setupSimStateListener() {
    _simStateEventChannel.receiveBroadcastStream().listen(
      (dynamic event) {
        try {
          final simStateChange = SimStateChange.fromJson(
            Map<String, dynamic>.from(event),
          );
          _handleSimStateChange(simStateChange);
        } catch (e) {
          debugPrint('Error processing SIM state change: $e');
        }
      },
      onError: (error) {
        debugPrint('SIM state listener error: $error');
        _error = error.toString();
        notifyListeners();
      },
    );
  }

  void _setupSimStateChangeListener() {
    _simStateChangeChannel.setMethodCallHandler((call) async {
      if (call.method == 'onSimStateChanged') {
        try {
          final isValid = call.arguments['isValid'] as bool? ?? true;
          if (!isValid) {
            // Notify about unauthorized SIM change
            _error =
                'Unauthorized SIM card change detected. SMS service has been paused.';
            notifyListeners();

            // You might want to show a notification or dialog to the user here
            // For now, we'll just update the error state
          }
        } catch (e) {
          debugPrint('Error handling SIM state change event: $e');
        }
      }
    });
  }

  void _handleSimStateChange(SimStateChange change) {
    try {
      _simStateBox?.put('latest_change', change);

      dynamic simData = change.simState;

      // Check if it's a string
      if (simData is String) {
        simData = simData.trim();

        // If empty or invalid JSON, skip
        if (simData.isEmpty ||
            (!simData.startsWith('[') && !simData.startsWith('{'))) {
          debugPrint(
            '[SimProvider] Empty or invalid SIM data string: "$simData"',
          );
          return;
        }

        simData = jsonDecode(simData);
      }

      // Ensure it's a list before mapping
      if (simData is List) {
        final simCards = simData
            .map((data) => SimCardInfo.fromJson(data as Map<String, dynamic>))
            .toList();

        _simInfo = SimInfo(
          simCards: simCards,
          activeSimCount: simCards.length,
          phoneCount: simCards.length,
        );

        debugPrint(
          '[SimProvider] SIM state changed - ${simCards.length} SIMs detected',
        );
        notifyListeners();
      } else {
        debugPrint(
          '[SimProvider] Unexpected SIM data type: ${simData.runtimeType}',
        );
      }
    } catch (e, stack) {
      debugPrint('Error handling SIM state change: $e\n$stack');
    }
  }

  List<SimCardInfo> _parseSimState(String simState) {
    try {
      final List<dynamic> simList = List<dynamic>.from(
        Map<String, dynamic>.from({'sims': simState})['sims'] ?? [],
      );

      return simList
          .map((sim) => SimCardInfo.fromJson(Map<String, dynamic>.from(sim)))
          .toList();
    } catch (e) {
      debugPrint('Error parsing SIM state: $e');
      return [];
    }
  }

  Future<String?> getStoredSimState() async {
    try {
      final result = await _simStateMonitorChannel.invokeMethod(
        'getStoredSimState',
      );
      return result as String?;
    } catch (e) {
      debugPrint('Error getting stored SIM state: $e');
      return null;
    }
  }

  Future<void> clearStoredSimState() async {
    try {
      await _simStateMonitorChannel.invokeMethod('clearStoredSimState');
      await _simStateBox?.clear();
    } catch (e) {
      debugPrint('Error clearing stored SIM state: $e');
    }
  }

  /// Clears all SIM-related local storage data
  Future<void> clearAllSimData() async {
    try {
      await _simStateBox?.clear();
      // Clear any other SIM-related data here if needed
      debugPrint('All SIM-related data cleared successfully');
    } catch (e) {
      debugPrint('Error clearing SIM data: $e');
      rethrow;
    }
  }

  Future<List<SimCardInfo>> getUnverifiedSimChanges() async {
    try {
      final result = await _simStateMonitorChannel.invokeMethod(
        'getUnverifiedSimChanges',
      );
      if (result != null) {
        final List<dynamic> simList = List<dynamic>.from(result);
        return simList
            .map((sim) => SimCardInfo.fromJson(Map<String, dynamic>.from(sim)))
            .toList();
      }
      return [];
    } catch (e) {
      debugPrint('Error getting unverified SIM changes: $e');
      return [];
    }
  }

  List<SimCardInfo> get activeSimCards {
    return _simInfo?.simCards
            .where((sim) => sim.subscriptionId != -1)
            .toList() ??
        [];
  }

  @override
  void dispose() {
    _simStateBox?.close();
    super.dispose();
  }
}
