import 'dart:async';

import 'package:android_package_installer/android_package_installer.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:com.mynotify.app/core/config/api_config.dart';
import 'package:com.mynotify.app/services/network/api_service.dart';

/// Provider version of your AutoUpdateCubit/State
class AutoUpdateProvider extends ChangeNotifier {
  AutoUpdateProvider() {
    Future.delayed(Duration.zero).then((_) async => await checkForUpdate());
  }
  // State fields (from AutoUpdateState)
  bool isChecking = true;
  bool isDownloading = false;
  bool updateAvailable = false;
  bool updateDownloaded = false;
  double progress = 0.0;

  String? currentVersion;
  String? latestVersion;
  String? errorMessage;

  /// Check for updates (migrated from Cubit)
  Future<void> checkForUpdate() async {
    isChecking = true;
    errorMessage = null;
    notifyListeners();

    try {
      final apiServices = ApiService();
      int retryCount = 0;
      Timer.periodic(const Duration(milliseconds: 500), (timer) async {
        if (apiServices.initialized) {
          timer.cancel();
          final response = await apiServices.checkForUpdates();
          debugPrint('Response: $response');
          final info = await PackageInfo.fromPlatform();
          currentVersion = info.version;
          final currentBuild = info.buildNumber;
          latestVersion = response.keys.contains('latest_version')
              ? response['latest_version'] ?? '1.0.0'
              : '1.0.0';
          final latestBuild = 1;

          updateAvailable = isCurrentVersionOlder(
            currentVersion: currentVersion!,
            currentBuild: int.parse(currentBuild),
            newVersion: latestVersion!,
            newBuild: latestBuild,
          );
          isChecking = false;
          notifyListeners();
        } else {
          retryCount++;
          if (retryCount >= 7) {
            timer.cancel();
            isChecking = false;
            notifyListeners();
          }
        }
      });
    } catch (e) {
      errorMessage = e.toString();
      isChecking = false;
      notifyListeners();
    }
  }

  /// check if current version is older than available version
  bool isCurrentVersionOlder({
    required String currentVersion,
    required int currentBuild,
    required String newVersion,
    required int newBuild,
  }) {
    debugPrint('====>>${currentVersion} : ${newVersion}');
    List<int> parseVersion(String version) =>
        version.split('.').map(int.parse).toList();

    final currentParts = parseVersion(currentVersion);
    final newParts = parseVersion(newVersion);

    for (var i = 0; i < 3; i++) {
      if (currentParts[i] < newParts[i]) return true;
      if (currentParts[i] > newParts[i]) return false;
    }

    return currentBuild < newBuild;
  }

  /// Start downloading the update
  Future<void> downloadUpdate() async {
    if (!updateAvailable || isDownloading) return;

    isDownloading = true;
    updateDownloaded = false;
    progress = 0.0;
    errorMessage = null;
    notifyListeners();

    try {
      final dir = await getTemporaryDirectory();
      final apkPath = '${dir.path}/update.apk';
      final apkUrl = '${ApiConfig.apiDomain}/api/apk-download/download/DEPOSIT';
      debugPrint('Starting download: $apkUrl');

      await ApiService().downloadApkFile(
        apkUrl,
        apkPath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            progress = received / total;
            updateDownloaded = progress >= 1.0;
            isDownloading = !updateDownloaded;
            notifyListeners();
          }
        },
      );
    } catch (e, stackTrace) {
      debugPrint('Download error: $e');
      debugPrint('StackTrace: $stackTrace');
      isDownloading = false;
      errorMessage = e.toString();
      notifyListeners();
    }
  }

  /// Cancel download
  void cancelDownload() {
    isDownloading = false;
    progress = 0.0;
    notifyListeners();
  }

  /// Reset state
  void reset() {
    isDownloading = false;
    updateDownloaded = false;
    progress = 0.0;
    errorMessage = null;
    notifyListeners();
  }

  /// Install update (simulate or hook into platform installer)
  Future<void> installUpdate() async {
    if (!updateDownloaded) return;

    try {
      final dir = await getTemporaryDirectory();
      final apkPath = '${dir.path}/update.apk';
      await AndroidPackageInstaller.installApk(apkFilePath: apkPath);

      updateAvailable = false;
      updateDownloaded = false;
      progress = 0.0;
    } catch (e) {
      errorMessage = e.toString();
    }

    notifyListeners();
  }

  /// Reset error state
  void clearError() {
    errorMessage = null;
    notifyListeners();
  }

  /// Reset update flags (e.g., after user dismisses)
  void clearUpdateFlag() {
    updateAvailable = false;
    updateDownloaded = false;
    progress = 0.0;
    notifyListeners();
  }

  @override
  void dispose() {
    reset();
    super.dispose();
  }
}
