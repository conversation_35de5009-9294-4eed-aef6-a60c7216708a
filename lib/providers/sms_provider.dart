import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:hive/hive.dart';
import 'dart:convert';
import '../models/sms_message.dart';
import 'auth_provider.dart';
import '../services/network/api_service.dart';
import '../core/database/database_helper.dart';
import '../core/network/connectivity_service.dart';
import '../services/native_logger.dart';

class SmsProvider extends ChangeNotifier {
  static const MethodChannel _smsChannel = MethodChannel(
    'com.mynotify.app/native',
  );
  static const EventChannel _smsEventChannel = EventChannel(
    'com.mynotify.app/native/events',
  );

  bool _isListening = false;
  bool _isLoading = false;
  String? _error;
  Box<SmsMessage>? _smsBox;
  List<SmsMessage> _recentMessages = [];
  List<PendingSmsMessage> _pendingMessages = [];
  int _pendingCount = 0;
  final DatabaseHelper _dbHelper = DatabaseHelper();
  bool _isProcessingMessages = false;
  final ConnectivityService connectivityService = ConnectivityService();
  AuthProvider? _authProvider;
  List<Map<String, dynamic>> _verifiedSims = [];
  final _logger = NativeLogger();

  bool get isListening => _isListening;
  bool get isLoading => _isLoading;
  String? get error => _error;
  List<SmsMessage> get recentMessages => _recentMessages;
  List<PendingSmsMessage> get pendingMessages => _pendingMessages;
  int get pendingCount => _pendingCount;

  SmsProvider({AuthProvider? authProvider}) : _authProvider = authProvider {
    _initializeSmsProvider();
  }

  /// Helper method to safely convert dynamic values to int
  /// Handles both int and double types that might come from native Android code
  int _toInt(dynamic value) {
    if (value is int) {
      return value;
    } else if (value is double) {
      return value.toInt();
    } else if (value is String) {
      return int.tryParse(value) ?? 0;
    } else {
      return 0;
    }
  }

  Future<void> _initializeSmsProvider() async {
    try {
      _smsBox = await Hive.openBox<SmsMessage>('sms_box_messages');

      await _loadStoredMessages();
      _setupSmsListener();
      await checkListenerStatus();

      // Load pending count from database
      _pendingCount = await _dbHelper.getPendingMessageCount();

      // Initialize connectivity service
      await connectivityService.initialize();
      connectivityService.addListener(_onNetworkRestored);

      notifyListeners();
    } catch (e) {
      debugPrint('Error initializing SMS provider: $e');
      _error = e.toString();
      notifyListeners();
    }
  }

  Future<void> _loadStoredMessages() async {
    try {
      _recentMessages = _smsBox?.values.toList() ?? [];
      // Load pending messages from SQLite database
      _pendingMessages = await _dbHelper.getMessagesForRetry();
      _pendingCount = _pendingMessages.length;
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading stored messages: $e');
    }
  }

  Future<bool> startSmsListener({
    String? endpoint,
    String? token,
    List<dynamic>? verifiedSims,
  }) async {
    await _logger.logSms(
      'SMS listener start requested',
      details: {
        'endpoint': endpoint ?? 'null',
        'hasToken': token != null,
        'verifiedSimsCount': verifiedSims?.length ?? 0,
      },
    );

    try {
      _isLoading = true;
      _error = null;
      // Store verified SIMs for later use
      if (verifiedSims != null) {
        _verifiedSims = List<Map<String, dynamic>>.from(verifiedSims);
      } else {
        _verifiedSims = [];
      }
      notifyListeners();

      final result = await _smsChannel.invokeMethod('startSmsListener', {
        'endpoint': endpoint,
        'token': token,
        'verifiedSims': verifiedSims != null ? jsonEncode(verifiedSims) : '[]',
      });
      _isListening = result as bool? ?? false;

      debugPrint('SMS listener started: $_isListening');
      await _logger.logSms(
        _isListening
            ? 'SMS listener started successfully'
            : 'SMS listener failed to start',
        details: {'isListening': _isListening},
      );
      return _isListening;
    } catch (e) {
      debugPrint('Failed to start SMS listener: $e');
      _error = e.toString();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> stopSmsListener() async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final result = await _smsChannel.invokeMethod('stopSmsListener');
      _isListening = !(result as bool? ?? true);

      debugPrint('SMS listener stopped: ${!_isListening}');
      return result as bool? ?? false;
    } catch (e) {
      debugPrint('Failed to stop SMS listener: $e');
      _error = e.toString();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> checkListenerStatus() async {
    try {
      final result = await _smsChannel.invokeMethod('isListening');
      _isListening = result as bool? ?? false;
      notifyListeners();
      return _isListening;
    } catch (e) {
      debugPrint('Failed to check SMS listener status: $e');
      return false;
    }
  }

  void _setupSmsListener() {
    _smsEventChannel.receiveBroadcastStream().listen(
      (dynamic event) {
        try {
          // Extract the actual SMS data from the event structure
          final eventData = Map<String, dynamic>.from(event);
          final smsData = eventData['data'] != null
              ? Map<String, dynamic>.from(eventData['data'])
              : eventData;
          _handleIncomingSms(smsData);
        } catch (e) {
          debugPrint('Error processing incoming SMS: $e');
        }
      },
      onError: (error) {
        debugPrint('SMS listener error: $error');
        _error = error.toString();
        notifyListeners();
      },
    );
  }

  void _handleIncomingSms(dynamic smsData) {
    _handleIncomingSmsAsync(smsData);
  }

  void _handleIncomingSmsAsync(dynamic smsData) async {
    try {
      debugPrint('Received SMS data: $smsData');

      // Extract SIM information
      final simSlot = _toInt(smsData['simSlot'] ?? -1);
      final subscriptionId = _toInt(smsData['subscriptionId'] ?? -1);

      debugPrint(
        'Extracted SIM slot: $simSlot, subscriptionId: $subscriptionId',
      );

      await _logger.logSms(
        'SMS received',
        details: {
          'sender': smsData['sender']?.toString() ?? 'unknown',
          'simSlot': simSlot,
          'subscriptionId': subscriptionId,
          'message': smsData['message']?.toString() ?? 'unknown',
          'timestamp': smsData['timestamp']?.toString() ?? 'unknown',
        },
      );

      // Check if SIM is authorized
      final isAuthorized = await _isSimAuthorized(simSlot, subscriptionId);
      if (!isAuthorized) {
        debugPrint(
          'SMS from unauthorized SIM - slot: $simSlot, subscriptionId: $subscriptionId',
        );
        await _logger.logSms(
          'SMS from unauthorized SIM rejected',
          details: {
            'simSlot': simSlot,
            'subscriptionId': subscriptionId,
            'sender': smsData['sender']?.toString() ?? 'unknown',
            'message': smsData['message']?.toString() ?? 'unknown',
          },
        );
        _showSecurityAlert();
        return;
      }

      // Create SMS message object
      final message = SmsMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        sender: smsData['sender']?.toString() ?? '',
        body: smsData['message']?.toString() ?? '',
        timestamp: DateTime.fromMillisecondsSinceEpoch(
          _toInt(smsData['timestamp'] ?? DateTime.now().millisecondsSinceEpoch),
        ),
        simSlot: simSlot,
        subscriptionId: subscriptionId,
        isSent: true, // Mark as sent since native service already sent it
        retryCount: 0,
      );

      // Store message locally for history
      await _logger.logSms(
        'SMS message storing locally',
        details: {
          'messageId': message.id,
          'sender': message.sender,
          'simSlot': message.simSlot,
          'subscriptionId': message.subscriptionId,
          'message': message.body,
        },
      );

      _storeSmsMessage(message);

      // Note: Not sending to backend since native Android service already handles this
      // The native service sends directly to the API and we receive success confirmation
      await _logger.logSms(
        'SMS message added to recent messages for UI',
        details: {
          'messageId': message.id,
          'recentMessagesCount': _recentMessages.length + 1,
        },
      );

      // Add to recent messages for UI
      _recentMessages.insert(0, message);
      if (_recentMessages.length > 10) {
        _recentMessages.removeLast();
      }
      notifyListeners();

      debugPrint(
        'SMS received from ${message.sender} on SIM ${message.simSlot}',
      );
    } catch (e) {
      debugPrint('Error handling incoming SMS: $e');
    }
  }

  // Security check to verify if SIM is authorized (like React Native)
  Future<bool> _isSimAuthorized(int simSlot, int subscriptionId) async {
    try {
      // If we don't have access to AuthProvider, allow the message for now
      if (_authProvider == null) {
        debugPrint('AuthProvider not available, allowing message');
        return true;
      }

      // Get current SIM info
      final simResult = await _smsChannel.invokeMethod('getSimInfo');
      if (simResult == null) return false;

      final simInfo = Map<String, dynamic>.from(simResult);
      final activeSimCards = List<dynamic>.from(simInfo['simCards'] ?? []);

      // Find the current SIM
      final currentSim = activeSimCards.firstWhere(
        (sim) =>
            sim['slotIndex'] == simSlot &&
            sim['subscriptionId'] == subscriptionId,
        orElse: () => null,
      );

      if (currentSim == null) {
        debugPrint(
          'Current SIM not found for slot: $simSlot, subscriptionId: $subscriptionId',
        );
        return false;
      }

      // Check if this SIM is verified in user's verified SIMs
      final verifiedSims = _authProvider!.verifiedSims;

      // Look for a matching verified SIM
      final isVerified = verifiedSims.any(
        (verifiedSim) =>
            verifiedSim.slotIndex == simSlot &&
            verifiedSim.subscriptionId == subscriptionId &&
            verifiedSim.iccId == currentSim['iccId'],
      );

      if (!isVerified) {
        debugPrint(
          'SIM not verified - slot: $simSlot, subscriptionId: $subscriptionId',
        );
        debugPrint('Current SIM: $currentSim');
        debugPrint(
          'Verified SIMs: ${verifiedSims.map((s) => {'slotIndex': s.slotIndex, 'subscriptionId': s.subscriptionId, 'iccId': s.iccId})}',
        );
      }

      return isVerified;
    } catch (e) {
      debugPrint('Error checking SIM authorization: $e');
      return false;
    }
  }

  void _showSecurityAlert() {
    // This would need a context or a global alert system
    // For now, we'll just log it - in a real app, you'd show a proper alert
    debugPrint(
      '[SECURITY ALERT] SMS monitoring stopped due to unauthorized SIM card changes',
    );
  }

  Future<void> _storeSmsMessage(SmsMessage message) async {
    try {
      await _smsBox?.put(message.id, message);
      _recentMessages = _smsBox?.values.toList() ?? [];

      // Keep only recent messages (last 100)
      if (_recentMessages.length > 100) {
        _recentMessages = _recentMessages.take(100).toList();
      }

      await _logger.logSms(
        'SMS message stored successfully in local storage',
        details: {
          'messageId': message.id,
          'sender': message.sender,
          'totalStoredMessages': _recentMessages.length,
          'simSlot': message.simSlot,
          'subscriptionId': message.subscriptionId,
        },
      );

      notifyListeners();
    } catch (e) {
      debugPrint('Error storing SMS message: $e');
      await _logger.logError(
        'Failed to store SMS message locally: $e',
        details: {
          'messageId': message.id,
          'sender': message.sender,
          'simSlot': message.simSlot,
          'subscriptionId': message.subscriptionId,
        },
      );
    }
  }

  Future<bool> _sendSmsToBackend(SmsMessage message) async {
    await _logger.logSms(
      'SMS backend send process started',
      details: {
        'messageId': message.id,
        'sender': message.sender,
        'simSlot': message.simSlot,
        'subscriptionId': message.subscriptionId,
        'verifiedSimsCount': _verifiedSims.length,
      },
    );

    try {
      // Find the receiver number from verified SIMs based on subscriptionId or simSlot
      String receiverNumber = '';
      for (var sim in _verifiedSims) {
        // Match by subscriptionId if available, otherwise by simSlot
        if ((sim['subscriptionId'] != null &&
                _toInt(sim['subscriptionId']) == message.subscriptionId) ||
            (sim['slotIndex'] != null &&
                _toInt(sim['slotIndex']) == message.simSlot)) {
          receiverNumber = sim['phone_number']?.toString() ?? '';
          break;
        }
      }

      await _logger.logSms(
        'SMS receiver number resolved',
        details: {
          'messageId': message.id,
          'receiverNumber': receiverNumber.isNotEmpty
              ? receiverNumber
              : 'NOT_FOUND',
          'simSlot': message.simSlot,
          'subscriptionId': message.subscriptionId,
        },
      );

      // Use API service to send SMS to backend
      final apiService = ApiService();
      await _logger.logSms(
        'SMS sending to backend API via ApiService',
        details: {
          'messageId': message.id,
          'endpoint': '/api/transaction/verify',
          'receiverNumber': receiverNumber,
        },
      );

      final success = await apiService.sendSmsToBackend(
        message,
        receiverNumber: receiverNumber,
      );

      if (success) {
        await _logger.logSms(
          'SMS successfully sent to backend - updating local storage',
          details: {
            'messageId': message.id,
            'sender': message.sender,
            'receiverNumber': receiverNumber,
          },
        );

        // Update message as sent
        final updatedMessage = message.copyWith(isSent: true);
        await _smsBox?.put(message.id, updatedMessage);
        _recentMessages = _smsBox?.values.toList() ?? [];
        notifyListeners();

        await _logger.logSms(
          'SMS marked as sent in local storage',
          details: {'messageId': message.id, 'isSent': true},
        );
      } else {
        await _logger.logSms(
          'SMS failed to send to backend',
          details: {
            'messageId': message.id,
            'sender': message.sender,
            'receiverNumber': receiverNumber,
            'error': 'ApiService returned false',
          },
        );
      }

      return success;
    } catch (e) {
      debugPrint('Error sending SMS to backend: $e');
      await _logger.logError(
        'SMS backend send process failed: $e',
        details: {
          'messageId': message.id,
          'sender': message.sender,
          'simSlot': message.simSlot,
          'subscriptionId': message.subscriptionId,
        },
      );
      return false;
    }
  }

  Future<void> _storePendingSms(SmsMessage message) async {
    try {
      final pendingMessage = PendingSmsMessage(
        messageId: message.id,
        sender: message.sender,
        body: message.body,
        receivedAt: message.timestamp,
        simSlot: message.simSlot,
        subscriptionId: message.subscriptionId,
      );

      // Store in SQLite database for transactional safety
      await _dbHelper.insertPendingMessage(pendingMessage);
      _pendingMessages = await _dbHelper.getMessagesForRetry();
      _pendingCount = _pendingMessages.length;
      notifyListeners();
    } catch (e) {
      debugPrint('Error storing pending SMS: $e');
    }
  }

  Future<void> retryPendingMessages() async {
    if (_isProcessingMessages) {
      debugPrint('Messages already being processed, skipping');
      await _logger.logSms(
        'Retry pending messages skipped - already processing',
        details: {'isProcessingMessages': _isProcessingMessages},
      );
      return;
    }

    _isProcessingMessages = true;
    debugPrint('Starting to process pending messages in order...');

    try {
      // Get fresh list of messages for retry - these are already ordered by created_at
      _pendingMessages = await _dbHelper.getMessagesForRetry();

      if (_pendingMessages.isEmpty) {
        debugPrint('No pending messages to process');
        return;
      }

      await _logger.logSms(
        'Found pending messages to retry',
        details: {'pendingMessagesCount': _pendingMessages.length},
      );

      debugPrint('Processing ${_pendingMessages.length} messages in order:');
      for (var i = 0; i < _pendingMessages.length; i++) {
        final pendingMsg = _pendingMessages[i];
        debugPrint(
          '  ${i + 1}. ID: ${pendingMsg.messageId}, Sender: ${pendingMsg.sender}, Received: ${pendingMsg.receivedAt.toIso8601String()}',
        );
      }

      // Process messages one by one in order
      for (final pendingMsg in _pendingMessages) {
        await _logger.logSms(
          'Processing pending SMS message',
          details: {
            'messageId': pendingMsg.messageId,
            'sender': pendingMsg.sender,
            'simSlot': pendingMsg.simSlot,
            'subscriptionId': pendingMsg.subscriptionId,
            'retryCount': pendingMsg.retryCount,
            'receivedAt': pendingMsg.receivedAt.toIso8601String(),
          },
        );

        await _logger.logSms(
          'Retrying SMS message to backend API',
          details: {
            'messageId': pendingMsg.messageId,
            'sender': pendingMsg.sender,
            'currentRetryAttempt': pendingMsg.retryCount + 1,
            'body': pendingMsg.body,
            'simSlot': pendingMsg.simSlot,
            'subscriptionId': pendingMsg.subscriptionId,
            'lastRetryAt': pendingMsg.lastRetryAt?.toIso8601String(),
            'lastError': pendingMsg.lastError,
          },
        );

        final smsMessage = SmsMessage(
          id: pendingMsg.messageId,
          sender: pendingMsg.sender,
          body: pendingMsg.body,
          timestamp: pendingMsg.receivedAt,
          simSlot: pendingMsg.simSlot,
          subscriptionId: pendingMsg.subscriptionId,
          retryCount: pendingMsg.retryCount,
        );

        final success = await _sendSmsToBackend(smsMessage);

        if (success) {
          await _logger.logSms(
            'Pending SMS successfully sent - removing from pending database',
            details: {
              'messageId': pendingMsg.messageId,
              'sender': pendingMsg.sender,
              'retryCount': pendingMsg.retryCount,
            },
          );
          // Remove from pending database
          await _dbHelper.deletePendingMessage(pendingMsg.messageId);

          await _logger.logSms(
            'SMS message removed from local pending storage',
            details: {
              'messageId': pendingMsg.messageId,
              'sender': pendingMsg.sender,
              'body': pendingMsg.body,
              'retryCount': pendingMsg.retryCount,
              'action': 'Successfully sent to backend',
            },
          );
        } else {
          await _logger.logSms(
            'Pending SMS failed to send - updating retry count',
            details: {
              'messageId': pendingMsg.messageId,
              'sender': pendingMsg.sender,
              'currentRetryCount': pendingMsg.retryCount,
              'newRetryCount': pendingMsg.retryCount + 1,
              'error': _error ?? 'Unknown error',
            },
          );
          // Update retry count with exponential backoff
          final updatedPending = pendingMsg.copyWith(
            retryCount: pendingMsg.retryCount + 1,
            lastRetryAt: DateTime.now(),
            lastError: _error,
          );
          await _dbHelper.updatePendingMessage(updatedPending);
        }
      }

      // Refresh pending messages and count
      _pendingMessages = await _dbHelper.getMessagesForRetry();
      _pendingCount = _pendingMessages.length;
      notifyListeners();
    } catch (e) {
      debugPrint('Error retrying pending messages: $e');
    } finally {
      _isProcessingMessages = false;
    }
  }

  Future<void> clearPendingMessages() async {
    try {
      await _dbHelper.clearPendingMessages();
      _pendingMessages = [];
      _pendingCount = 0;
      notifyListeners();
    } catch (e) {
      debugPrint('Error clearing pending messages: $e');
    }
  }

  Future<void> clearAllMessages() async {
    try {
      await _smsBox?.clear();
      await _dbHelper.clearAllData();
      _recentMessages = [];
      _pendingMessages = [];
      _pendingCount = 0;
      notifyListeners();
      debugPrint('All SMS-related data cleared successfully');
    } catch (e) {
      debugPrint('Error clearing all messages: $e');
      rethrow;
    }
  }

  /// Clears all SMS-related local storage data (alias for clearAllMessages)
  Future<void> clearAllSmsData() async {
    return clearAllMessages();
  }

  // Clear all local data
  Future<void> clearAllVeriableData() async {
    try {
      await _smsBox?.clear();
      await _dbHelper.clearAllData();
      _recentMessages = [];
      _pendingMessages = [];
      _pendingCount = 0;
      _verifiedSims = [];
      _isListening = false;
      _isLoading = false;
      _error = null;
      notifyListeners();
      debugPrint('All local data cleared successfully');
    } catch (e) {
      debugPrint('Error clearing all local data: $e');
      rethrow;
    }
  }

  @override
  void dispose() {
    _smsBox?.close();
    _dbHelper.close();
    connectivityService.removeListener(_onNetworkRestored);
    super.dispose();
  }

  /// Callback when internet access is restored
  void _onNetworkRestored() {
    // Only retry if we actually have internet access
    if (connectivityService.hasInternetAccess) {
      debugPrint('Internet access restored, retrying pending messages');
      _logger.logNetwork(
        'Internet access restored - triggering SMS retry',
        details: {
          'hasInternetAccess': connectivityService.hasInternetAccess,
          'pendingMessagesCount': _pendingCount,
        },
      );
      // Use addPostFrameCallback to avoid calling setState during build
      WidgetsBinding.instance.addPostFrameCallback((_) {
        retryPendingMessages();
      });
    } else {
      debugPrint(
        'Network connectivity restored but no internet access, skipping retry',
      );
      _logger.logNetwork(
        'Network connectivity restored but no internet access',
        details: {
          'hasInternetAccess': connectivityService.hasInternetAccess,
          'isConnected': connectivityService.isConnected,
          'pendingMessagesCount': _pendingCount,
        },
      );
    }
  }
}
