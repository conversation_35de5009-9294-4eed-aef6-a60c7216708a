package com.mynotify.app

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import android.provider.Telephony
import android.telephony.SmsMessage

/**
 * Static SMS Receiver for Android 15+ compatibility
 *
 * This receiver is registered in AndroidManifest.xml to ensure SMS broadcasts are received
 * even with Android 15's stricter security policies. It works alongside the dynamic receiver
 * in SmsListenerService to provide maximum reliability.
 *
 * Key features:
 * - Static registration ensures it receives broadcasts even when app is in background
 * - High priority (**********) to receive SMS before other apps
 * - Comprehensive logging to diagnose SMS reception issues
 * - Works independently of the SmsListenerService
 */
class SmsReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context?, intent: Intent?) {
        // CRITICAL: Log immediately when broadcast is received
        android.util.Log.d("SmsReceiver", "========================================")
        android.util.Log.d("SmsReceiver", "📱 STATIC SMS RECEIVER TRIGGERED!")
        android.util.Log.d("SmsReceiver", "Intent action: ${intent?.action}")
        android.util.Log.d("SmsReceiver", "Device: ${Build.MANUFACTURER} ${Build.MODEL}")
        android.util.Log.d("SmsReceiver", "Android version: ${Build.VERSION.SDK_INT}")
        android.util.Log.d("SmsReceiver", "========================================")

        if (context == null || intent == null) {
            android.util.Log.e("SmsReceiver", "Context or Intent is null!")
            return
        }

        // CRITICAL: Write to unified logging system immediately
        val loggerModule = LoggerModule(context)
        loggerModule.writeLog("STATIC SMS RECEIVER", buildString {
            appendLine("📱 STATIC SMS RECEIVER TRIGGERED!")
            appendLine("Intent Action: ${intent.action}")
            appendLine("Device: ${Build.MANUFACTURER} ${Build.MODEL}")
            appendLine("Android Version: ${Build.VERSION.SDK_INT}")
            appendLine("Timestamp: ${System.currentTimeMillis()}")
        })

        try {
            if (intent.action == Telephony.Sms.Intents.SMS_RECEIVED_ACTION) {
                android.util.Log.d("SmsReceiver", "SMS_RECEIVED_ACTION detected")

                loggerModule.writeLog("STATIC SMS RECEIVER", "SMS_RECEIVED_ACTION detected - processing...")
                
                // Extract messages
                val messages = Telephony.Sms.Intents.getMessagesFromIntent(intent)

                if (messages == null) {
                    android.util.Log.e("SmsReceiver", "getMessagesFromIntent returned NULL!")
                    loggerModule.writeLog("STATIC SMS ERROR", buildString {
                        appendLine("Error: getMessagesFromIntent returned NULL")
                        appendLine("This indicates SMS broadcast was received but messages couldn't be extracted")
                        appendLine("Possible causes:")
                        appendLine("  1. Missing SMS permissions")
                        appendLine("  2. Android 15 security restrictions")
                        appendLine("  3. Carrier-specific SMS blocking")
                    })
                    return
                }

                if (messages.isEmpty()) {
                    android.util.Log.e("SmsReceiver", "getMessagesFromIntent returned EMPTY array!")
                    loggerModule.writeLog("STATIC SMS ERROR", "Error: getMessagesFromIntent returned empty array")
                    return
                }

                android.util.Log.d("SmsReceiver", "Extracted ${messages.size} message(s)")

                loggerModule.writeLog("STATIC SMS RECEIVER", "Successfully extracted ${messages.size} message(s) from intent")

                // Log ALL intent extras with their values
                android.util.Log.d("SmsReceiver", "All intent extras:")
                intent.extras?.keySet()?.forEach { key ->
                    val value = intent.extras?.get(key)
                    android.util.Log.d("SmsReceiver", "  $key = $value (${value?.javaClass?.simpleName})")
                }

                // Log message details
                messages.forEachIndexed { index, msg ->
                    android.util.Log.d("SmsReceiver", "Message $index:")
                    android.util.Log.d("SmsReceiver", "  Sender: ${msg.displayOriginatingAddress}")
                    android.util.Log.d("SmsReceiver", "  Body: ${msg.displayMessageBody}")
                    android.util.Log.d("SmsReceiver", "  Timestamp: ${msg.timestampMillis}")

                    // Write to unified logging
                    loggerModule.writeLog("STATIC SMS DETAILS", buildString {
                        appendLine("Message $index:")
                        appendLine("Sender: ${msg.displayOriginatingAddress}")
                        appendLine("Body: ${msg.displayMessageBody}")
                        appendLine("Timestamp: ${msg.timestampMillis}")
                    })
                }

                // Extract subscription ID - try multiple keys
                android.util.Log.d("SmsReceiver", "Extracting subscription ID...")
                var subscriptionId = intent.getIntExtra("subscription", -1)
                android.util.Log.d("SmsReceiver", "  Tried 'subscription': $subscriptionId")

                if (subscriptionId == -1) {
                    subscriptionId = intent.getIntExtra("subscription_id", -1)
                    android.util.Log.d("SmsReceiver", "  Tried 'subscription_id': $subscriptionId")
                }
                if (subscriptionId == -1) {
                    subscriptionId = intent.getIntExtra("android.telephony.extra.SUBSCRIPTION_INDEX", -1)
                    android.util.Log.d("SmsReceiver", "  Tried 'android.telephony.extra.SUBSCRIPTION_INDEX': $subscriptionId")
                }
                if (subscriptionId == -1) {
                    subscriptionId = intent.getIntExtra("slot", -1)
                    android.util.Log.d("SmsReceiver", "  Tried 'slot': $subscriptionId")
                }
                if (subscriptionId == -1) {
                    subscriptionId = intent.getIntExtra("simId", -1)
                    android.util.Log.d("SmsReceiver", "  Tried 'simId': $subscriptionId")
                }
                if (subscriptionId == -1) {
                    subscriptionId = intent.getIntExtra("phone", -1)
                    android.util.Log.d("SmsReceiver", "  Tried 'phone': $subscriptionId")
                }

                android.util.Log.d("SmsReceiver", "Final subscription ID: $subscriptionId")

                // Write subscription ID to unified logging
                loggerModule.writeLog("STATIC SMS RECEIVER", buildString {
                    appendLine("Subscription ID extracted: $subscriptionId")
                    appendLine("SMS successfully received by static receiver")
                    appendLine("Note: If SmsListenerService is running, it should also receive this broadcast")
                })

                // Forward to SmsListenerService if it's running
                try {
                    // Create a new intent to forward to the service
                    val serviceIntent = Intent(context, SmsListenerService::class.java).apply {
                        action = "com.mynotify.app.SMS_RECEIVED_FORWARD"
                        putExtra("original_intent", intent)
                    }

                    android.util.Log.d("SmsReceiver", "Forwarding SMS to SmsListenerService...")
                    // Note: We can't directly call the service's method, but we can broadcast
                    // or use a custom action. For now, just log that we received it.

                    android.util.Log.d("SmsReceiver", "SMS successfully received by static receiver")
                    android.util.Log.d("SmsReceiver", "If SmsListenerService is running, it should also receive this broadcast")

                } catch (e: Exception) {
                    android.util.Log.e("SmsReceiver", "Error forwarding to service: ${e.message}")
                    android.util.Log.e("SmsReceiver", "Stack trace: ${e.stackTrace.joinToString("\n")}")

                    loggerModule.writeLog("STATIC SMS ERROR", buildString {
                        appendLine("Error forwarding to service: ${e.message}")
                        appendLine("Stack trace: ${e.stackTrace.joinToString("\n")}")
                    })
                }

            } else {
                android.util.Log.w("SmsReceiver", "Received intent with unexpected action: ${intent.action}")
                loggerModule.writeLog("STATIC SMS WARNING", "Received intent with unexpected action: ${intent.action}")
            }
        } catch (e: Exception) {
            android.util.Log.e("SmsReceiver", "Error processing SMS: ${e.message}")
            android.util.Log.e("SmsReceiver", "Stack trace: ${e.stackTrace.joinToString("\n")}")

            loggerModule.writeLog("STATIC SMS ERROR", buildString {
                appendLine("Error processing SMS: ${e.message}")
                appendLine("Stack trace: ${e.stackTrace.joinToString("\n")}")
            })
        }
    }
}

