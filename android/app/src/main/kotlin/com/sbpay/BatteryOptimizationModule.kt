package com.mynotify.app

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.PowerManager
import android.provider.Settings
import android.util.Log

class BatteryOptimizationModule(private val context: Context, private val activity: Activity?) {

    fun getName(): String {
        return "BatteryOptimizationModule"
    }

    // Removed @ReactMethod annotation - will use MethodChannel instead
    fun isIgnoringBatteryOptimizations(): Boolean {
        try {
            val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
            val packageName = context.packageName
            return powerManager.isIgnoringBatteryOptimizations(packageName)
        } catch (e: Exception) {
            Log.e("BatteryOptimizationModule", "Error checking battery optimization: ${e.message}")
            return false
        }
    }

    // Removed @ReactMethod annotation - will use MethodChannel instead
    fun requestBatteryOptimization(): Boolean {
        try {
            val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
            val packageName = context.packageName

            if (!powerManager.isIgnoringBatteryOptimizations(packageName)) {
                activity?.let {
                    val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
                        data = Uri.parse("package:$packageName")
                    }
                    it.startActivity(intent)
                    return true
                } ?: run {
                    Log.e("BatteryOptimizationModule", "Activity not found")
                    return false
                }
            } else {
                return false // Already optimized
            }
        } catch (e: Exception) {
            Log.e("BatteryOptimizationModule", "Error requesting battery optimization: ${e.message}")
            return false
        }
    }
}
