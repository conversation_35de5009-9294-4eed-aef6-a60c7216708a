package com.mynotify.app

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.os.Build
import android.util.Log
import com.tencent.mmkv.MMKV

class BootCompletedReceiver : BroadcastReceiver() {
    companion object {
        private const val PREFS_NAME = "SmsListenerPrefs"
        private const val KEY_API_ENDPOINT = "apiEndpoint"
        private const val KEY_API_HEADERS = "apiHeaders"
        private const val KEY_SERVICE_STATE = "service_state"
        private const val KEY_PREVIOUS_SIM_STATE = "previous_sim_state"
        private const val KEY_VERIFIED_SIMS = "verified_sims"

        fun saveServiceConfig(context: Context, endpoint: String, headers: Map<String, Any>, verifiedSims: String) {
            android.util.Log.d("BootCompletedReceiver", "Saving service configuration for auto-restart")
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            // Convert headers to JSON string for proper storage and retrieval
            val headersJson = try {
                val jsonObject = org.json.JSONObject()
                headers.forEach { (key, value) ->
                    jsonObject.put(key, value)
                }
                jsonObject.toString()
            } catch (e: Exception) {
                Log.e("BootCompletedReceiver", "Error converting headers to JSON: ${e.message}")
                "{}"
            }
            
            prefs.edit().apply {
                putString(KEY_API_ENDPOINT, endpoint)
                putString(KEY_API_HEADERS, headersJson)
                putString(KEY_VERIFIED_SIMS, verifiedSims)
                putBoolean(KEY_SERVICE_STATE, true)
                apply()
            }
                val endpoint1 = prefs.getString(KEY_API_ENDPOINT, null)
        val headersStr1 = prefs.getString(KEY_API_HEADERS, null)
        val verifiedSimsStr1 = prefs.getString(KEY_VERIFIED_SIMS, null)
            android.util.Log.d("BootCompletedReceiver", "Service configuration saved : $endpoint1, $headersStr1, $verifiedSimsStr1")
        }

        fun clearServiceConfig(context: Context) {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            prefs.edit().apply {
                putBoolean(KEY_SERVICE_STATE, false)
                remove(KEY_VERIFIED_SIMS)
                apply()
            }
        }
    }

    override fun onReceive(context: Context?, intent: Intent?) {
        if (context == null || intent?.action != Intent.ACTION_BOOT_COMPLETED) {
            return
        }

        android.util.Log.d("BootCompletedReceiver", "Device boot completed, checking services configuration")
        MMKV.initialize(context)

        // Log device boot event
        try {
            val loggerModule = LoggerModule(context)
            loggerModule.logDeviceBoot()
        } catch (e: Exception) {
            android.util.Log.w("BootCompletedReceiver", "Failed to log device boot: ${e.message}")
        }
        // Start SIM state monitoring first
        val simMonitorIntent = Intent(context, SimStateMonitorService::class.java)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.startForegroundService(simMonitorIntent)
        } else {
            context.startService(simMonitorIntent)
        }

        // Check if SMS service was running before reboot
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val serviceState = prefs.getBoolean(KEY_SERVICE_STATE, false)
        
        if (!serviceState) {
            android.util.Log.d("BootCompletedReceiver", "SMS Service was not running before reboot")
            return
        }

        // Wait for SIM state monitor to initialize
        Thread.sleep(2000)

        // Get current SIM state and compare with previous state
        val mmkv = com.tencent.mmkv.MMKV.defaultMMKV()
        val currentSimState = mmkv?.decodeString(SimStateMonitorModule.SIM_STATE_KEY)
        val previousSimState = prefs.getString(KEY_PREVIOUS_SIM_STATE, null)
        
        if (previousSimState != null && currentSimState != previousSimState) {
            android.util.Log.d("BootCompletedReceiver", "SIM state changed during reboot, not starting SMS service")
            return
        }

        val endpoint = prefs.getString(KEY_API_ENDPOINT, null)
        val headersStr = prefs.getString(KEY_API_HEADERS, null)
        val verifiedSimsStr = prefs.getString(KEY_VERIFIED_SIMS, null)
        android.util.Log.d("BootCompletedReceiver", "Starting SMS service with endpoint: $endpoint, headers: $headersStr, verified SIMs: $verifiedSimsStr")

        if (endpoint == null || headersStr == null) {
            android.util.Log.d("BootCompletedReceiver", "Missing configuration data")
            return
        }

        try {
            var token: String? = null
try {
    val headersJson = org.json.JSONObject(headersStr)
    val authorization = headersJson.optString("Authorization", "")
    if (authorization.startsWith("Bearer ")) {
        token = authorization.removePrefix("Bearer ").trim()
    }
} catch (e: Exception) {
    android.util.Log.e("BootCompletedReceiver", "Error parsing headers JSON: ${e.message}")
}
            // Start SMS listener service
            val serviceIntent = Intent(context, SmsListenerService::class.java).apply {
                putExtra("api_endpoint", endpoint)
                putExtra("api_headers", headersStr)
                 putExtra("api_token", token)
                if (verifiedSimsStr != null) {
                    putExtra("verified_sims", verifiedSimsStr)
                }
                addFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES)
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent)
            } else {
                context.startService(serviceIntent)
            }
            android.util.Log.d("BootCompletedReceiver", "Services started successfully after reboot")
        } catch (e: Exception) {
            android.util.Log.e("BootCompletedReceiver", "Error starting services: ${e.message}")
        }
    }

}