package com.mynotify.app

import android.content.Context
import android.telephony.SubscriptionInfo
import android.telephony.SubscriptionManager
import android.telephony.TelephonyManager
import android.util.Log
import org.json.JSONArray
import org.json.JSONObject

class SimInfoModule(private val context: Context) {
    companion object {
        const val NAME = "SimInfoModule"
    }

    fun getName(): String = NAME

    // Removed @ReactMethod annotation - will use MethodChannel
    fun getSimInfo(): String {
        try {
            val subscriptionManager = context.getSystemService(Context.TELEPHONY_SUBSCRIPTION_SERVICE) as SubscriptionManager
            val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
            
            val activeSubscriptionInfoList = subscriptionManager.activeSubscriptionInfoList
            val simInfoArray = JSONArray()

            if (activeSubscriptionInfoList != null) {
                for (subscriptionInfo in activeSubscriptionInfoList) {
                    val simInfoMap = JSONObject().apply {
                        put("slotIndex", subscriptionInfo.simSlotIndex)
                        put("carrierName", subscriptionInfo.carrierName?.toString() ?: "")
                        put("displayName", subscriptionInfo.displayName?.toString() ?: "")
                        put("iccId", subscriptionInfo.iccId ?: "")
                        put("countryIso", subscriptionInfo.countryIso ?: "")
                        put("isEmbedded", subscriptionInfo.isEmbedded)
                        put("subscriptionId", subscriptionInfo.subscriptionId)
                        
                        // Try to get phone number with multiple fallback approaches
                        var phoneNumber = ""
                        
                        // First try: Get from subscription info
                        try {
                            phoneNumber = subscriptionInfo.number?.trim() ?: ""
                        } catch (e: Exception) {
                            Log.w("SimInfoModule", "Failed to get phone number from subscription info: ${e.message}")
                        }
                        
                        // Second try: Use subscription-specific TelephonyManager for Android S and above
                        if (phoneNumber.isEmpty() && android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                            try {
                                val subscriptionSpecificTelephonyManager = telephonyManager.createForSubscriptionId(subscriptionInfo.subscriptionId)
                                phoneNumber = subscriptionSpecificTelephonyManager.line1Number?.trim() ?: ""
                            } catch (e: Exception) {
                                Log.w("SimInfoModule", "Failed to get phone number from subscription-specific TelephonyManager: ${e.message}")
                            }
                        }
                        
                        // Third try: Use general TelephonyManager
                        if (phoneNumber.isEmpty()) {
                            try {
                                phoneNumber = telephonyManager.line1Number?.trim() ?: ""
                            } catch (e: Exception) {
                                Log.w("SimInfoModule", "Failed to get phone number from general TelephonyManager: ${e.message}")
                            }
                        }
                        
                        // Log if phone number is still empty
                        if (phoneNumber.isEmpty()) {
                            Log.w("SimInfoModule", "Phone number is empty for subscriptionId: ${subscriptionInfo.subscriptionId}")
                        } else {
                            Log.d("SimInfoModule", "Successfully retrieved phone number for subscriptionId ${subscriptionInfo.subscriptionId}: $phoneNumber")
                        }
                        
                        put("phoneNumber", phoneNumber)
                    }
                    simInfoArray.put(simInfoMap)
                }
            }

            val resultMap = JSONObject().apply {
                put("simCards", simInfoArray)
                put("activeSimCount", activeSubscriptionInfoList?.size ?: 0)
                put("phoneCount", telephonyManager.phoneCount)
            }

            Log.d("SimInfoModule", "Returning SIM info: ${resultMap.toString()}")
            return resultMap.toString()
        } catch (e: SecurityException) {
            Log.e("SimInfoModule", "Permission denied: ${e.message}")
            return "{\"error\": \"PERMISSION_DENIED\", \"message\": \"Required permissions are not granted\"}"
        } catch (e: Exception) {
            Log.e("SimInfoModule", "Error getting SIM info: ${e.message}")
            return "{\"error\": \"ERROR\", \"message\": \"Failed to get SIM information: ${e.message}\"}"
        }
    }
}