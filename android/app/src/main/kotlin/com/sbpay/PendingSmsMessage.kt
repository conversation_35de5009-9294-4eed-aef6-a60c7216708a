package com.mynotify.app

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "pending_sms_messages")
data class PendingSmsMessage(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val sender: String?,
    val message: String?,
    val timestamp: Long,
    val paymentType: String?,
    val receiverNumber: String = "unknown",
    val retryCount: Int = 0,
    val lastRetryTimestamp: Long = 0  // Start with 0 so message can be retried immediately
)